import fs from "fs"
import { PNG } from "pngjs"

/* Config */

let width = 128
let height = 128

/* reduce size to fit in discord message */
const fit = true
/* account for increased message size from nitro */
const nitro = true

const transparency = false
const alphaDithering = false

/* doubles horizontal resolution */
const doubleHorizontalResolution = true

/* signature to add to the end of the image */
const signature: string = ""

/* Config */

const characterLimit = nitro ? 4000 : 2000

const characters: string[] = ["█"]
if (transparency) {
	if (alphaDithering) {
		characters.push("▓")
		characters.push("▒")
		characters.push("░")
	}
	characters.push(" ")
}

type Color = [number, number, number]

const ansiEscape = "\u001b["

const palette: Color[] = [
	[79, 80, 90],
	[220, 50, 47],
	[133, 153, 0],
	[181, 137, 0],
	[38, 139, 210],
	[211, 54, 130],
	[42, 161, 152],
	[255, 255, 255],
]
const paletteCodes: number[] = [30, 31, 32, 33, 34, 35, 36, 37]

/**
 * Finds the index of the closest color in `colors` to the `target`.
 *
 * @param target - [r, g, b] target color values 0–255
 * @param colors - array of [r, g, b] colors to compare
 * @returns index of the closest match
 */
function findClosestColor(target: Color, colors: Color[]): number {
	let bestIndex = 0
	let bestDist = Infinity

	const [r1, g1, b1] = target

	for (let i = 0; i < colors.length; i++) {
		const [r2, g2, b2] = colors[i]
		const dr = r1 - r2
		const dg = g1 - g2
		const db = b1 - b2
		const dist = dr * dr + dg * dg + db * db // squared Euclidean distance

		if (dist < bestDist) {
			bestDist = dist
			bestIndex = i
		}
	}

	return bestIndex
}

function resize<T extends PNG>(src: T, newWidth: number, newHeight: number): T {
	const dst = new PNG({ width: newWidth, height: newHeight })
	const xRatio = src.width / newWidth
	const yRatio = src.height / newHeight

	for (let y = 0; y < newHeight; y++) {
		const srcY = Math.round(y * yRatio)
		for (let x = 0; x < newWidth; x++) {
			const srcX = Math.round(x * xRatio)
			const dstIdx = (newWidth * y + x) << 2
			const srcIdx = (src.width * srcY + srcX) << 2
			dst.data[dstIdx] = src.data[srcIdx]
			dst.data[dstIdx + 1] = src.data[srcIdx + 1]
			dst.data[dstIdx + 2] = src.data[srcIdx + 2]
			dst.data[dstIdx + 3] = src.data[srcIdx + 3]
		}
	}

	return Object.assign({}, src, dst)
}

function textToBinary(text: string) {
	return text
		.split("")
		.map(char => {
			return char.charCodeAt(0).toString(2).padStart(8, "0")
		})
		.join(";")
}

function toAnsi(png: PNG): string {
	fs.writeFileSync("debug-before.png", PNG.sync.write(png))

	/* resize taking into account doubleHorizontalResolution */
	if (doubleHorizontalResolution) {
		png = resize(png, width * 2, height * 2)
	} else {
		png = resize(png, width, height)
	}

	fs.writeFileSync("debug-after.png", PNG.sync.write(png))

	let lastColorIndex: number | undefined
	let out = ""

	for (let i = 0; i < png.data.length; i += 4) {
		const pixelIndex = i / 4
		const row = Math.floor(pixelIndex / png.width)

		// Skip every other row if doubleHorizontalResolution is enabled to maintain aspect ratio
		if (row % 2 !== 0 && doubleHorizontalResolution) {
			continue
		}

		if (i % (png.width * 4) === 0 && i !== 0) {
			out += "\r\n"
		}

		const color: Color = [png.data[i], png.data[i + 1], png.data[i + 2]]
		const alpha = png.data[i + 3]

		let character = characters[characters.length - 1 - Math.round((alpha / 255) * (characters.length - 1))]
		/* double character if doubleHorizontalResolution isn't enabled to maintain aspect ratio */
		if (!doubleHorizontalResolution) character += character

		const index = findClosestColor(color, palette)

		if ((lastColorIndex === undefined || lastColorIndex !== index) && character[0] !== " ") {
			out += `${ansiEscape}0;${paletteCodes[index]}m${character}`
			lastColorIndex = index
		} else {
			out += character
		}
	}

	if (signature !== "") {
		out = "```ansi\n" + out + `${ansiEscape}${textToBinary(signature)}m` + "\n```"
	} else {
		out = "```ansi\n" + out + "\n```"
	}

	return out
}

const file = fs.readFileSync("input.png")
const png = PNG.sync.read(file)
let out = ""

if (fit) {
	while (true) {
		console.log(`trying ${width}x${height}`)
		out = toAnsi(png)
		if (out.length < characterLimit) {
			break
		}
		console.log(`too big ${out.length} > ${characterLimit}`)
		/* reduce size taking into account doubleHorizontalResolution */
		if (doubleHorizontalResolution) {
			width -= 1
			height -= 1
		} else {
			width--
			height--
		}
	}
} else {
	out = toAnsi(png)
}

console.log(`output ${width}x${height} ${out.length} characters`)
fs.writeFileSync("output.txt", out)
