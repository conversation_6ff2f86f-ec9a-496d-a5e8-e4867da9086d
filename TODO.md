✅ Fixed: Sometimes when doubleHorizontalResolution is true the image becomes a little smaller horizontally

-   Fixed by changing resize from (width*2, height*2) to (width\*2, height) and removing row skipping
-   This maintains consistent aspect ratio sampling

Add error handling for file operations and invalid PNG data

Make input/output paths configurable via command line arguments

Maybe: Implement perceptual color distance (like Delta E) for better color matching
